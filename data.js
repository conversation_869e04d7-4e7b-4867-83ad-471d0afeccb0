/**
 * Data Management Layer
 * Handles localStorage operations, data validation, and budget calculations
 */

class BudgetData {
    constructor() {
        this.storageKey = 'budgetAllocatorData';
        this.data = this.loadData();
        this.observers = [];
    }

    /**
     * Load data from localStorage with fallback to default structure
     */
    loadData() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const parsed = JSON.parse(stored);
                return this.validateAndMigrateData(parsed);
            }
        } catch (error) {
            console.warn('Failed to load data from localStorage:', error);
        }
        
        return this.getDefaultData();
    }

    /**
     * Get default data structure
     */
    getDefaultData() {
        return {
            version: '1.0',
            income: 0,
            categories: [],
            lastModified: new Date().toISOString()
        };
    }

    /**
     * Validate and migrate data structure if needed
     */
    validateAndMigrateData(data) {
        // Ensure required properties exist
        const validated = {
            version: data.version || '1.0',
            income: this.validateNumber(data.income, 0),
            categories: Array.isArray(data.categories) ? data.categories : [],
            lastModified: data.lastModified || new Date().toISOString()
        };

        // Validate each category
        validated.categories = validated.categories
            .map(category => this.validateCategory(category))
            .filter(category => category !== null);

        return validated;
    }

    /**
     * Validate a single category object
     */
    validateCategory(category) {
        if (!category || typeof category !== 'object') return null;

        const validated = {
            id: category.id || this.generateId(),
            name: this.validateString(category.name, ''),
            amount: this.validateNumber(category.amount, 0),
            description: this.validateString(category.description, ''),
            subcategories: Array.isArray(category.subcategories) ? category.subcategories : [],
            createdAt: category.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // Validate subcategories
        validated.subcategories = validated.subcategories
            .map(sub => this.validateCategory(sub))
            .filter(sub => sub !== null);

        return validated.name ? validated : null;
    }

    /**
     * Validate and sanitize string input
     */
    validateString(value, defaultValue = '') {
        return typeof value === 'string' ? value.trim() : defaultValue;
    }

    /**
     * Validate and sanitize number input
     */
    validateNumber(value, defaultValue = 0) {
        const num = parseFloat(value);
        return !isNaN(num) && num >= 0 ? num : defaultValue;
    }

    /**
     * Generate unique ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Save data to localStorage
     */
    saveData() {
        try {
            this.data.lastModified = new Date().toISOString();
            localStorage.setItem(this.storageKey, JSON.stringify(this.data));
            this.notifyObservers();
            return true;
        } catch (error) {
            console.error('Failed to save data to localStorage:', error);
            return false;
        }
    }

    /**
     * Add observer for data changes
     */
    addObserver(callback) {
        this.observers.push(callback);
    }

    /**
     * Remove observer
     */
    removeObserver(callback) {
        this.observers = this.observers.filter(obs => obs !== callback);
    }

    /**
     * Notify all observers of data changes
     */
    notifyObservers() {
        this.observers.forEach(callback => {
            try {
                callback(this.data);
            } catch (error) {
                console.error('Observer callback error:', error);
            }
        });
    }

    /**
     * Set monthly income
     */
    setIncome(amount) {
        const validAmount = this.validateNumber(amount, 0);
        this.data.income = validAmount;
        return this.saveData();
    }

    /**
     * Get monthly income
     */
    getIncome() {
        return this.data.income;
    }

    /**
     * Add new category
     */
    addCategory(categoryData) {
        const category = this.validateCategory({
            ...categoryData,
            id: this.generateId(),
            createdAt: new Date().toISOString()
        });

        if (!category) return null;

        this.data.categories.push(category);
        this.saveData();
        return category;
    }

    /**
     * Update existing category
     */
    updateCategory(id, updates) {
        const index = this.data.categories.findIndex(cat => cat.id === id);
        if (index === -1) return null;

        const updated = {
            ...this.data.categories[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };

        const validated = this.validateCategory(updated);
        if (!validated) return null;

        this.data.categories[index] = validated;
        this.saveData();
        return validated;
    }

    /**
     * Delete category
     */
    deleteCategory(id) {
        const index = this.data.categories.findIndex(cat => cat.id === id);
        if (index === -1) return false;

        this.data.categories.splice(index, 1);
        return this.saveData();
    }

    /**
     * Get all categories
     */
    getCategories() {
        return [...this.data.categories];
    }

    /**
     * Get category by ID
     */
    getCategory(id) {
        return this.data.categories.find(cat => cat.id === id) || null;
    }

    /**
     * Add subcategory to a category
     */
    addSubcategory(parentId, subcategoryData) {
        const parent = this.getCategory(parentId);
        if (!parent) return null;

        const subcategory = this.validateCategory({
            ...subcategoryData,
            id: this.generateId(),
            createdAt: new Date().toISOString()
        });

        if (!subcategory) return null;

        parent.subcategories.push(subcategory);
        parent.updatedAt = new Date().toISOString();
        this.saveData();
        return subcategory;
    }

    /**
     * Calculate total allocated amount
     */
    getTotalAllocated() {
        return this.data.categories.reduce((total, category) => {
            return total + category.amount;
        }, 0);
    }

    /**
     * Calculate remaining budget
     */
    getRemainingBudget() {
        return this.data.income - this.getTotalAllocated();
    }

    /**
     * Calculate allocation percentage
     */
    getAllocationPercentage() {
        if (this.data.income === 0) return 0;
        return Math.min((this.getTotalAllocated() / this.data.income) * 100, 100);
    }

    /**
     * Check if allocation exceeds income
     */
    isOverAllocated() {
        return this.getTotalAllocated() > this.data.income;
    }

    /**
     * Get budget summary
     */
    getBudgetSummary() {
        const totalAllocated = this.getTotalAllocated();
        const remaining = this.getRemainingBudget();
        const percentage = this.getAllocationPercentage();

        return {
            income: this.data.income,
            totalAllocated,
            remaining,
            percentage,
            isOverAllocated: this.isOverAllocated(),
            categoryCount: this.data.categories.length
        };
    }

    /**
     * Export data for backup
     */
    exportData() {
        return JSON.stringify(this.data, null, 2);
    }

    /**
     * Import data from backup
     */
    importData(jsonString) {
        try {
            const imported = JSON.parse(jsonString);
            const validated = this.validateAndMigrateData(imported);
            this.data = validated;
            return this.saveData();
        } catch (error) {
            console.error('Failed to import data:', error);
            return false;
        }
    }

    /**
     * Clear all data
     */
    clearData() {
        this.data = this.getDefaultData();
        return this.saveData();
    }
}

// Create global instance
window.budgetData = new BudgetData();
