# Budget Allocator

A mobile-first personal budget allocation web application built with vanilla HTML, CSS, and JavaScript. Organize your monthly income across custom categories and subcategories with real-time calculations and validation.

## Features

### 🏦 Income Management
- Set and update monthly income with currency formatting (South African Rand)
- Real-time validation for positive numbers only
- Persistent storage using localStorage

### 📊 Category Management
- Create unlimited custom budget categories
- Edit category names, amounts, and descriptions
- Delete categories with confirmation dialogs
- Drag-and-drop reordering (visual feedback)

### 🔄 Subcategory System
- Add unlimited subcategories to any main category
- Nested allocation validation (subcategories cannot exceed parent category)
- Independent CRUD operations for subcategories
- Expandable/collapsible subcategory sections

### 💰 Real-time Calculations
- Auto-calculate remaining budget: Total Income - Sum of allocations
- Category-level remaining amounts with progress bars
- Visual warnings for over-allocation scenarios
- Percentage-based allocation tracking

### 📱 Mobile-First Design
- Responsive design optimized for 320px - 768px screens
- Touch-friendly interface with 44px minimum touch targets
- High contrast colors for accessibility (WCAG 2.1 AA compliant)
- No horizontal scrolling on any device size

### 🔒 Data Persistence
- Client-side storage using localStorage
- Automatic data validation and migration
- Graceful error handling for corrupted data
- Export/import functionality for backups

## Technical Architecture

### File Structure
```
budget-allocator/
├── index.html          # Main HTML structure with semantic markup
├── styles.css          # Mobile-first responsive CSS
├── script.js           # Application logic and DOM manipulation
├── data.js             # Data management and localStorage operations
└── README.md           # Documentation
```

### Technology Stack
- **HTML5**: Semantic markup with proper ARIA labels
- **CSS3**: Mobile-first responsive design with CSS Grid and Flexbox
- **JavaScript ES6+**: Modular architecture with classes and modern syntax
- **localStorage**: Client-side data persistence

### Browser Compatibility
- Chrome 60+ (mobile and desktop)
- Safari 12+ (mobile and desktop)
- Firefox 55+ (mobile and desktop)
- Edge 79+ (mobile and desktop)

## Getting Started

### Installation
1. Clone or download the repository
2. Open `index.html` in a web browser
3. No build process or dependencies required

### Usage

#### Setting Up Your Budget
1. **Enter Monthly Income**: Input your total monthly income in the income field
2. **Add Categories**: Click "Add Category" to create budget categories (e.g., "Housing", "Food", "Transportation")
3. **Set Allocations**: Assign specific amounts to each category
4. **Monitor Progress**: View real-time calculations and remaining budget

#### Managing Categories
- **Edit**: Click the pencil icon to modify category details
- **Delete**: Click the trash icon to remove categories (with confirmation)
- **Add Subcategories**: Click the "+" button to add subcategories to any category

#### Working with Subcategories
- **Expand/Collapse**: Click the arrow icon to show/hide subcategories
- **Add**: Use the "Add Subcategory" button within expanded categories
- **Validate**: Subcategory amounts are automatically validated against parent category limits

## Data Structure

### Category Object
```javascript
{
  id: "unique_identifier",
  name: "Category Name",
  amount: 1500.00,
  description: "Optional description",
  subcategories: [
    {
      id: "sub_unique_identifier",
      name: "Subcategory Name",
      amount: 500.00,
      description: "Optional description",
      createdAt: "2024-01-01T00:00:00.000Z",
      updatedAt: "2024-01-01T00:00:00.000Z"
    }
  ],
  createdAt: "2024-01-01T00:00:00.000Z",
  updatedAt: "2024-01-01T00:00:00.000Z"
}
```

### Storage Format
```javascript
{
  version: "1.0",
  income: 6000.00,
  categories: [...],
  lastModified: "2024-01-01T00:00:00.000Z"
}
```

## Accessibility Features

### WCAG 2.1 AA Compliance
- **Keyboard Navigation**: Full keyboard support for all interactive elements
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Color Contrast**: High contrast ratios meeting accessibility standards
- **Focus Management**: Clear focus indicators and logical tab order

### Responsive Design Breakpoints
- **320px**: Small phones (minimum supported width)
- **375px**: Standard phones (optimized layout)
- **768px**: Tablets and larger screens

## Performance Optimizations

### Loading Performance
- Minimal CSS and JavaScript footprint
- No external dependencies or frameworks
- Efficient DOM manipulation without unnecessary re-renders
- Optimized for 3G connections

### Memory Management
- Event listener cleanup
- Efficient data structures
- Minimal memory footprint
- Proper garbage collection

## Error Handling

### Input Validation
- Real-time form validation with user-friendly error messages
- Boundary value testing for all numeric inputs
- XSS prevention through proper HTML escaping
- Data sanitization and validation

### Data Recovery
- Automatic data validation on load
- Graceful degradation for localStorage unavailability
- Data migration for version compatibility
- Backup and restore functionality

## Development

### Code Quality
- **Modular Architecture**: Clear separation of concerns
- **Consistent Naming**: camelCase for JavaScript, kebab-case for CSS
- **Comprehensive Comments**: Detailed documentation throughout
- **Error Handling**: Robust error handling and user feedback

### Testing Recommendations
1. **Device Testing**: Test on actual mobile devices
2. **Browser Testing**: Verify compatibility across supported browsers
3. **Accessibility Testing**: Use screen readers and keyboard navigation
4. **Performance Testing**: Monitor loading times and memory usage

## Contributing

### Code Style
- Use consistent indentation (2 spaces)
- Follow existing naming conventions
- Add comprehensive comments for new features
- Maintain mobile-first responsive design principles

### Feature Requests
- Ensure compatibility with existing data structure
- Maintain accessibility standards
- Consider mobile performance impact
- Follow progressive enhancement principles

## License

This project is open source and available under the MIT License.

## Support

For issues, questions, or feature requests, please refer to the code comments and documentation within the source files.
