<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Personal Budget Allocation - Manage your monthly budget with custom categories and subcategories">
    <title>Budget Allocator</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header Section -->
    <header class="header" role="banner">
        <div class="container">
            <h1 class="app-title">Budget Allocator</h1>
            <div class="income-display" aria-live="polite">
                <span class="income-label">Monthly Income:</span>
                <span class="income-amount" id="incomeDisplay">R0</span>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main" role="main">
        <div class="container">
            <!-- Income Input Section -->
            <section class="income-section" aria-labelledby="income-heading">
                <h2 id="income-heading" class="section-title">Set Monthly Income</h2>
                <div class="input-group">
                    <label for="incomeInput" class="input-label">Enter your monthly income:</label>
                    <div class="currency-input">
                        <span class="currency-symbol">R</span>
                        <input 
                            type="number" 
                            id="incomeInput" 
                            class="income-input" 
                            placeholder="0.00"
                            min="0"
                            step="0.01"
                            aria-describedby="income-error"
                            autocomplete="off"
                        >
                    </div>
                    <div id="income-error" class="error-message" role="alert" aria-live="assertive"></div>
                </div>
            </section>

            <!-- Budget Summary Dashboard -->
            <section class="summary-section" aria-labelledby="summary-heading">
                <h2 id="summary-heading" class="section-title">Budget Overview</h2>
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="card-label">Total Income</div>
                        <div class="card-value" id="summaryIncome">R0</div>
                    </div>
                    <div class="summary-card">
                        <div class="card-label">Total Allocated</div>
                        <div class="card-value" id="summaryAllocated">R0</div>
                    </div>
                    <div class="summary-card remaining">
                        <div class="card-label">Remaining</div>
                        <div class="card-value" id="summaryRemaining">R0</div>
                    </div>
                </div>
                <div class="allocation-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="allocationProgress" style="width: 0%"></div>
                    </div>
                    <div class="progress-text">
                        <span id="allocationPercentage">0%</span> allocated
                    </div>
                </div>
            </section>

            <!-- Categories Section -->
            <section class="categories-section" aria-labelledby="categories-heading">
                <div class="section-header">
                    <h2 id="categories-heading" class="section-title">Budget Categories</h2>
                    <button 
                        class="btn btn-primary add-category-btn" 
                        id="addCategoryBtn"
                        aria-label="Add new budget category"
                    >
                        <span class="btn-icon">+</span>
                        Add Category
                    </button>
                </div>
                
                <div class="categories-list" id="categoriesList" role="list">
                    <!-- Categories will be dynamically inserted here -->
                </div>

                <div class="empty-state" id="emptyState">
                    <div class="empty-icon">📊</div>
                    <h3>No categories yet</h3>
                    <p>Start by adding your first budget category to organize your expenses.</p>
                </div>
            </section>
        </div>
    </main>

    <!-- Category Modal -->
    <div class="modal-overlay" id="categoryModal" role="dialog" aria-labelledby="modal-title" aria-hidden="true">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title" class="modal-title">Add Category</h3>
                <button class="modal-close" id="modalClose" aria-label="Close modal">&times;</button>
            </div>
            <form class="modal-body" id="categoryForm" novalidate>
                <div class="form-group">
                    <label for="categoryName" class="form-label">Category Name *</label>
                    <input 
                        type="text" 
                        id="categoryName" 
                        class="form-input" 
                        maxlength="50" 
                        required
                        aria-describedby="name-error"
                    >
                    <div id="name-error" class="error-message" role="alert"></div>
                </div>
                
                <div class="form-group">
                    <label for="categoryAmount" class="form-label">Allocated Amount *</label>
                    <div class="currency-input">
                        <span class="currency-symbol">R</span>
                        <input 
                            type="number" 
                            id="categoryAmount" 
                            class="form-input" 
                            min="0" 
                            step="0.01" 
                            required
                            aria-describedby="amount-error"
                        >
                    </div>
                    <div id="amount-error" class="error-message" role="alert"></div>
                </div>
                
                <div class="form-group">
                    <label for="categoryDescription" class="form-label">Description (Optional)</label>
                    <textarea 
                        id="categoryDescription" 
                        class="form-textarea" 
                        maxlength="200" 
                        rows="3"
                        placeholder="Brief description of this category..."
                    ></textarea>
                    <div class="char-counter">
                        <span id="descriptionCounter">0</span>/200 characters
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="modalCancel">Cancel</button>
                <button type="submit" class="btn btn-primary" id="modalSave" form="categoryForm">Save Category</button>
            </div>
        </div>
    </div>

    <!-- Alert System -->
    <div class="alert-container" id="alertContainer" aria-live="polite"></div>

    <!-- Scripts -->
    <script src="data.js"></script>
    <script src="script.js"></script>
</body>
</html>
