/**
 * Budget Allocator - Main Application Logic
 * Handles DOM manipulation, user interactions, and UI updates
 */

class BudgetApp {
    constructor() {
        this.data = window.budgetData;
        this.currentEditingCategory = null;
        this.isEditMode = false;
        
        this.initializeElements();
        this.bindEvents();
        this.setupDataObserver();
        this.updateUI();
    }

    /**
     * Initialize DOM element references
     */
    initializeElements() {
        // Income elements
        this.incomeInput = document.getElementById('incomeInput');
        this.incomeDisplay = document.getElementById('incomeDisplay');
        this.incomeError = document.getElementById('income-error');

        // Summary elements
        this.summaryIncome = document.getElementById('summaryIncome');
        this.summaryAllocated = document.getElementById('summaryAllocated');
        this.summaryRemaining = document.getElementById('summaryRemaining');
        this.allocationProgress = document.getElementById('allocationProgress');
        this.allocationPercentage = document.getElementById('allocationPercentage');

        // Categories elements
        this.categoriesList = document.getElementById('categoriesList');
        this.emptyState = document.getElementById('emptyState');
        this.addCategoryBtn = document.getElementById('addCategoryBtn');

        // Modal elements
        this.categoryModal = document.getElementById('categoryModal');
        this.modalTitle = document.getElementById('modal-title');
        this.categoryForm = document.getElementById('categoryForm');
        this.categoryName = document.getElementById('categoryName');
        this.categoryAmount = document.getElementById('categoryAmount');
        this.categoryDescription = document.getElementById('categoryDescription');
        this.descriptionCounter = document.getElementById('descriptionCounter');
        this.modalClose = document.getElementById('modalClose');
        this.modalCancel = document.getElementById('modalCancel');
        this.modalSave = document.getElementById('modalSave');

        // Error elements
        this.nameError = document.getElementById('name-error');
        this.amountError = document.getElementById('amount-error');

        // Alert container
        this.alertContainer = document.getElementById('alertContainer');
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Income input events
        this.incomeInput.addEventListener('input', this.handleIncomeInput.bind(this));
        this.incomeInput.addEventListener('blur', this.handleIncomeBlur.bind(this));

        // Category management events
        this.addCategoryBtn.addEventListener('click', this.openAddCategoryModal.bind(this));
        this.categoryForm.addEventListener('submit', this.handleCategorySubmit.bind(this));

        // Modal events
        this.modalClose.addEventListener('click', this.closeModal.bind(this));
        this.modalCancel.addEventListener('click', this.closeModal.bind(this));
        this.categoryModal.addEventListener('click', this.handleModalOverlayClick.bind(this));

        // Form validation events
        this.categoryName.addEventListener('input', this.validateCategoryName.bind(this));
        this.categoryAmount.addEventListener('input', this.validateCategoryAmount.bind(this));
        this.categoryDescription.addEventListener('input', this.updateDescriptionCounter.bind(this));

        // Keyboard events
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }

    /**
     * Setup data observer for real-time updates
     */
    setupDataObserver() {
        this.data.addObserver(() => {
            this.updateUI();
        });
    }

    /**
     * Handle income input changes
     */
    handleIncomeInput(event) {
        const value = event.target.value;
        const numValue = parseFloat(value);
        
        // Clear previous errors
        this.clearError(this.incomeError);
        
        // Validate input
        if (value && (isNaN(numValue) || numValue < 0)) {
            this.showError(this.incomeError, 'Please enter a valid positive amount');
            return;
        }
        
        // Update data
        this.data.setIncome(numValue || 0);
    }

    /**
     * Handle income input blur (format currency)
     */
    handleIncomeBlur(event) {
        const value = parseFloat(event.target.value) || 0;
        event.target.value = value.toFixed(2);
    }

    /**
     * Open add category modal
     */
    openAddCategoryModal() {
        this.isEditMode = false;
        this.currentEditingCategory = null;
        this.modalTitle.textContent = 'Add Category';
        this.modalSave.textContent = 'Save Category';
        this.resetForm();
        this.openModal();
    }

    /**
     * Open edit category modal
     */
    openEditCategoryModal(categoryId) {
        const category = this.data.getCategory(categoryId);
        if (!category) return;

        this.isEditMode = true;
        this.currentEditingCategory = category;
        this.modalTitle.textContent = 'Edit Category';
        this.modalSave.textContent = 'Update Category';
        
        // Populate form with category data
        this.categoryName.value = category.name;
        this.categoryAmount.value = category.amount.toFixed(2);
        this.categoryDescription.value = category.description || '';
        this.updateDescriptionCounter();
        
        this.openModal();
    }

    /**
     * Open modal
     */
    openModal() {
        this.categoryModal.setAttribute('aria-hidden', 'false');
        this.categoryModal.classList.add('active');
        this.categoryName.focus();
        document.body.style.overflow = 'hidden';
    }

    /**
     * Close modal
     */
    closeModal() {
        this.categoryModal.setAttribute('aria-hidden', 'true');
        this.categoryModal.classList.remove('active');
        document.body.style.overflow = '';
        this.resetForm();
    }

    /**
     * Handle modal overlay click
     */
    handleModalOverlayClick(event) {
        if (event.target === this.categoryModal) {
            this.closeModal();
        }
    }

    /**
     * Handle category form submission
     */
    handleCategorySubmit(event) {
        event.preventDefault();

        if (!this.validateForm()) {
            return;
        }

        const categoryData = {
            name: this.categoryName.value.trim(),
            amount: parseFloat(this.categoryAmount.value),
            description: this.categoryDescription.value.trim()
        };

        let success = false;

        if (this.currentParentCategory) {
            // Working with subcategory
            if (this.isEditMode && this.currentEditingCategory) {
                // Update existing subcategory
                const subcategoryIndex = this.currentParentCategory.subcategories.findIndex(
                    sub => sub.id === this.currentEditingCategory.id
                );

                if (subcategoryIndex !== -1) {
                    this.currentParentCategory.subcategories[subcategoryIndex] = {
                        ...this.currentParentCategory.subcategories[subcategoryIndex],
                        ...categoryData,
                        updatedAt: new Date().toISOString()
                    };
                    this.currentParentCategory.updatedAt = new Date().toISOString();
                    success = this.data.saveData();

                    if (success) {
                        this.showAlert('Subcategory updated successfully!', 'success');
                    }
                }
            } else {
                // Add new subcategory
                const added = this.data.addSubcategory(this.currentParentCategory.id, categoryData);
                success = !!added;

                if (success) {
                    this.showAlert('Subcategory added successfully!', 'success');
                }
            }
        } else {
            // Working with main category
            if (this.isEditMode && this.currentEditingCategory) {
                // Update existing category
                const updated = this.data.updateCategory(this.currentEditingCategory.id, categoryData);
                success = !!updated;

                if (success) {
                    this.showAlert('Category updated successfully!', 'success');
                }
            } else {
                // Add new category
                const added = this.data.addCategory(categoryData);
                success = !!added;

                if (success) {
                    this.showAlert('Category added successfully!', 'success');
                }
            }
        }

        if (success) {
            this.closeModal();
        } else {
            this.showAlert('Failed to save. Please try again.', 'error');
        }
    }

    /**
     * Validate the category form
     */
    validateForm() {
        let isValid = true;

        // Validate name
        if (!this.validateCategoryName()) {
            isValid = false;
        }

        // Validate amount
        if (!this.validateCategoryAmount()) {
            isValid = false;
        }

        return isValid;
    }

    /**
     * Validate category name
     */
    validateCategoryName() {
        const name = this.categoryName.value.trim();
        
        if (!name) {
            this.showError(this.nameError, 'Category name is required');
            return false;
        }
        
        if (name.length > 50) {
            this.showError(this.nameError, 'Category name must be 50 characters or less');
            return false;
        }

        // Check for duplicate names (excluding current category in edit mode)
        const categories = this.data.getCategories();
        const duplicate = categories.find(cat => 
            cat.name.toLowerCase() === name.toLowerCase() && 
            (!this.currentEditingCategory || cat.id !== this.currentEditingCategory.id)
        );
        
        if (duplicate) {
            this.showError(this.nameError, 'A category with this name already exists');
            return false;
        }

        this.clearError(this.nameError);
        return true;
    }

    /**
     * Validate category amount
     */
    validateCategoryAmount() {
        const amount = parseFloat(this.categoryAmount.value);

        if (isNaN(amount) || amount < 0) {
            this.showError(this.amountError, 'Please enter a valid positive amount');
            return false;
        }

        if (this.currentParentCategory) {
            // Validating subcategory amount
            const parentAmount = this.currentParentCategory.amount;
            const currentSubcategoriesTotal = this.currentParentCategory.subcategories.reduce((sum, sub) => {
                // Exclude current subcategory if editing
                if (this.isEditMode && this.currentEditingCategory && sub.id === this.currentEditingCategory.id) {
                    return sum;
                }
                return sum + sub.amount;
            }, 0);

            const availableInParent = parentAmount - currentSubcategoriesTotal;

            if (amount > availableInParent) {
                this.showError(this.amountError,
                    `Amount exceeds parent category budget (R${availableInParent.toFixed(2)} available in ${this.currentParentCategory.name})`);
                return false;
            }
        } else {
            // Validating main category amount
            const currentIncome = this.data.getIncome();
            const currentAllocated = this.data.getTotalAllocated();
            const categoryCurrentAmount = this.isEditMode && this.currentEditingCategory ?
                this.currentEditingCategory.amount : 0;
            const availableAmount = currentIncome - currentAllocated + categoryCurrentAmount;

            if (amount > availableAmount) {
                this.showError(this.amountError,
                    `Amount exceeds available budget (R${availableAmount.toFixed(2)} available)`);
                return false;
            }
        }

        this.clearError(this.amountError);
        return true;
    }

    /**
     * Update description character counter
     */
    updateDescriptionCounter() {
        const length = this.categoryDescription.value.length;
        this.descriptionCounter.textContent = length;
        
        if (length > 200) {
            this.descriptionCounter.style.color = '#dc3545';
        } else {
            this.descriptionCounter.style.color = '#6c757d';
        }
    }

    /**
     * Reset form to initial state
     */
    resetForm() {
        this.categoryForm.reset();
        this.clearError(this.nameError);
        this.clearError(this.amountError);
        this.updateDescriptionCounter();
        this.currentEditingCategory = null;
        this.currentParentCategory = null;
        this.isEditMode = false;
    }

    /**
     * Show error message
     */
    showError(element, message) {
        element.textContent = message;
        element.style.display = 'block';
    }

    /**
     * Clear error message
     */
    clearError(element) {
        element.textContent = '';
        element.style.display = 'none';
    }

    /**
     * Handle keyboard events
     */
    handleKeyDown(event) {
        // Close modal on Escape key
        if (event.key === 'Escape' && this.categoryModal.classList.contains('active')) {
            this.closeModal();
        }
    }

    /**
     * Format currency for display
     */
    formatCurrency(amount) {
        return `R${amount.toFixed(2)}`;
    }

    /**
     * Update the entire UI
     */
    updateUI() {
        this.updateIncomeDisplay();
        this.updateSummary();
        this.updateCategoriesList();
    }

    /**
     * Update income display
     */
    updateIncomeDisplay() {
        const income = this.data.getIncome();
        this.incomeDisplay.textContent = this.formatCurrency(income);
        
        if (!this.incomeInput.matches(':focus')) {
            this.incomeInput.value = income.toFixed(2);
        }
    }

    /**
     * Update budget summary
     */
    updateSummary() {
        const summary = this.data.getBudgetSummary();

        this.summaryIncome.textContent = this.formatCurrency(summary.income);
        this.summaryAllocated.textContent = this.formatCurrency(summary.totalAllocated);
        this.summaryRemaining.textContent = this.formatCurrency(summary.remaining);

        // Update progress bar
        this.allocationProgress.style.width = `${Math.min(summary.percentage, 100)}%`;
        this.allocationPercentage.textContent = `${Math.round(summary.percentage)}%`;

        // Update styling based on allocation status
        const remainingCard = this.summaryRemaining.closest('.summary-card');
        const progressFill = this.allocationProgress;

        if (summary.isOverAllocated) {
            remainingCard.classList.add('over-allocated');
            remainingCard.classList.remove('remaining');
            progressFill.classList.add('over-allocated');
        } else {
            remainingCard.classList.remove('over-allocated');
            remainingCard.classList.add('remaining');
            progressFill.classList.remove('over-allocated');
        }
    }

    /**
     * Update categories list
     */
    updateCategoriesList() {
        const categories = this.data.getCategories();

        if (categories.length === 0) {
            this.categoriesList.style.display = 'none';
            this.emptyState.style.display = 'block';
            return;
        }

        this.categoriesList.style.display = 'flex';
        this.emptyState.style.display = 'none';

        this.categoriesList.innerHTML = categories.map(category =>
            this.renderCategory(category)
        ).join('');

        // Bind category events
        this.bindCategoryEvents();
    }

    /**
     * Render a single category
     */
    renderCategory(category) {
        const subcategoriesTotal = category.subcategories.reduce((sum, sub) => sum + sub.amount, 0);
        const remainingInCategory = category.amount - subcategoriesTotal;
        const hasSubcategories = category.subcategories.length > 0;

        return `
            <div class="category-item" data-category-id="${category.id}" role="listitem">
                <div class="category-header" ${hasSubcategories ? 'data-expandable="true"' : ''}>
                    <div class="category-info">
                        <div class="category-name">${this.escapeHtml(category.name)}</div>
                        <div class="category-amount">${this.formatCurrency(category.amount)}</div>
                        ${category.description ? `<div class="category-description">${this.escapeHtml(category.description)}</div>` : ''}
                        ${hasSubcategories ? `
                            <div class="category-progress">
                                <div class="category-progress-bar">
                                    <div class="category-progress-fill" style="width: ${Math.min((subcategoriesTotal / category.amount) * 100, 100)}%"></div>
                                </div>
                                <div class="progress-text" style="font-size: 0.8rem; color: #6c757d; margin-top: 4px;">
                                    ${this.formatCurrency(subcategoriesTotal)} of ${this.formatCurrency(category.amount)} allocated
                                    ${remainingInCategory < 0 ? `(${this.formatCurrency(Math.abs(remainingInCategory))} over)` : ''}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="category-actions">
                        ${hasSubcategories ? `
                            <button class="btn btn-secondary btn-small add-subcategory-btn"
                                    data-category-id="${category.id}"
                                    aria-label="Add subcategory to ${category.name}">
                                <span class="btn-icon">+</span>
                            </button>
                        ` : ''}
                        <button class="btn btn-edit btn-small edit-category-btn"
                                data-category-id="${category.id}"
                                aria-label="Edit ${category.name}">
                            ✏️
                        </button>
                        <button class="btn btn-danger btn-small delete-category-btn"
                                data-category-id="${category.id}"
                                aria-label="Delete ${category.name}">
                            🗑️
                        </button>
                        ${hasSubcategories ? `
                            <button class="btn btn-secondary btn-icon-only expand-btn"
                                    data-category-id="${category.id}"
                                    aria-label="Toggle subcategories">
                                <span class="expand-icon">▼</span>
                            </button>
                        ` : ''}
                    </div>
                </div>
                ${hasSubcategories ? this.renderSubcategories(category) : ''}
            </div>
        `;
    }

    /**
     * Render subcategories section
     */
    renderSubcategories(category) {
        return `
            <div class="subcategories expandable collapsed" data-category-id="${category.id}">
                <div class="subcategory-header">
                    <span>Subcategories (${category.subcategories.length})</span>
                    <button class="btn btn-primary btn-small add-subcategory-btn"
                            data-category-id="${category.id}">
                        <span class="btn-icon">+</span> Add Subcategory
                    </button>
                </div>
                <div class="subcategory-list">
                    ${category.subcategories.map(sub => this.renderSubcategory(sub, category.id)).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Render a single subcategory
     */
    renderSubcategory(subcategory, parentId) {
        return `
            <div class="subcategory-item" data-subcategory-id="${subcategory.id}" data-parent-id="${parentId}">
                <div class="subcategory-info">
                    <div class="subcategory-name">${this.escapeHtml(subcategory.name)}</div>
                    <div class="subcategory-amount">${this.formatCurrency(subcategory.amount)}</div>
                    ${subcategory.description ? `<div class="category-description" style="font-size: 0.8rem;">${this.escapeHtml(subcategory.description)}</div>` : ''}
                </div>
                <div class="subcategory-actions">
                    <button class="btn btn-edit btn-icon-only edit-subcategory-btn"
                            data-subcategory-id="${subcategory.id}"
                            data-parent-id="${parentId}"
                            aria-label="Edit ${subcategory.name}">
                        ✏️
                    </button>
                    <button class="btn btn-danger btn-icon-only delete-subcategory-btn"
                            data-subcategory-id="${subcategory.id}"
                            data-parent-id="${parentId}"
                            aria-label="Delete ${subcategory.name}">
                        🗑️
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Bind category-related events
     */
    bindCategoryEvents() {
        // Edit category buttons
        document.querySelectorAll('.edit-category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const categoryId = btn.dataset.categoryId;
                this.openEditCategoryModal(categoryId);
            });
        });

        // Delete category buttons
        document.querySelectorAll('.delete-category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const categoryId = btn.dataset.categoryId;
                this.deleteCategory(categoryId);
            });
        });

        // Expand/collapse buttons
        document.querySelectorAll('.expand-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const categoryId = btn.dataset.categoryId;
                this.toggleSubcategories(categoryId);
            });
        });

        // Add subcategory buttons
        document.querySelectorAll('.add-subcategory-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const categoryId = btn.dataset.categoryId;
                this.openAddSubcategoryModal(categoryId);
            });
        });

        // Edit subcategory buttons
        document.querySelectorAll('.edit-subcategory-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const subcategoryId = btn.dataset.subcategoryId;
                const parentId = btn.dataset.parentId;
                this.openEditSubcategoryModal(subcategoryId, parentId);
            });
        });

        // Delete subcategory buttons
        document.querySelectorAll('.delete-subcategory-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const subcategoryId = btn.dataset.subcategoryId;
                const parentId = btn.dataset.parentId;
                this.deleteSubcategory(subcategoryId, parentId);
            });
        });
    }

    /**
     * Open add subcategory modal
     */
    openAddSubcategoryModal(parentCategoryId) {
        this.isEditMode = false;
        this.currentEditingCategory = null;
        this.currentParentCategory = this.data.getCategory(parentCategoryId);

        if (!this.currentParentCategory) return;

        this.modalTitle.textContent = `Add Subcategory to ${this.currentParentCategory.name}`;
        this.modalSave.textContent = 'Save Subcategory';
        this.resetForm();
        this.openModal();
    }

    /**
     * Open edit subcategory modal
     */
    openEditSubcategoryModal(subcategoryId, parentCategoryId) {
        const parentCategory = this.data.getCategory(parentCategoryId);
        if (!parentCategory) return;

        const subcategory = parentCategory.subcategories.find(sub => sub.id === subcategoryId);
        if (!subcategory) return;

        this.isEditMode = true;
        this.currentEditingCategory = subcategory;
        this.currentParentCategory = parentCategory;
        this.modalTitle.textContent = `Edit ${subcategory.name}`;
        this.modalSave.textContent = 'Update Subcategory';

        // Populate form with subcategory data
        this.categoryName.value = subcategory.name;
        this.categoryAmount.value = subcategory.amount.toFixed(2);
        this.categoryDescription.value = subcategory.description || '';
        this.updateDescriptionCounter();

        this.openModal();
    }

    /**
     * Delete subcategory with confirmation
     */
    deleteSubcategory(subcategoryId, parentCategoryId) {
        const parentCategory = this.data.getCategory(parentCategoryId);
        if (!parentCategory) return;

        const subcategory = parentCategory.subcategories.find(sub => sub.id === subcategoryId);
        if (!subcategory) return;

        const message = `Are you sure you want to delete "${subcategory.name}"? This action cannot be undone.`;

        if (confirm(message)) {
            // Remove subcategory from parent
            const index = parentCategory.subcategories.findIndex(sub => sub.id === subcategoryId);
            if (index !== -1) {
                parentCategory.subcategories.splice(index, 1);
                parentCategory.updatedAt = new Date().toISOString();

                const success = this.data.saveData();
                if (success) {
                    this.showAlert('Subcategory deleted successfully!', 'success');
                } else {
                    this.showAlert('Failed to delete subcategory. Please try again.', 'error');
                }
            }
        }
    }

    /**
     * Delete category with confirmation
     */
    deleteCategory(categoryId) {
        const category = this.data.getCategory(categoryId);
        if (!category) return;

        const hasSubcategories = category.subcategories.length > 0;
        const message = hasSubcategories
            ? `Are you sure you want to delete "${category.name}" and all its subcategories? This action cannot be undone.`
            : `Are you sure you want to delete "${category.name}"? This action cannot be undone.`;

        if (confirm(message)) {
            const success = this.data.deleteCategory(categoryId);
            if (success) {
                this.showAlert('Category deleted successfully!', 'success');
            } else {
                this.showAlert('Failed to delete category. Please try again.', 'error');
            }
        }
    }

    /**
     * Toggle subcategories visibility
     */
    toggleSubcategories(categoryId) {
        const subcategoriesEl = document.querySelector(`.subcategories[data-category-id="${categoryId}"]`);
        const expandIcon = document.querySelector(`.expand-btn[data-category-id="${categoryId}"] .expand-icon`);

        if (!subcategoriesEl || !expandIcon) return;

        const isCollapsed = subcategoriesEl.classList.contains('collapsed');

        if (isCollapsed) {
            subcategoriesEl.classList.remove('collapsed');
            expandIcon.classList.add('expanded');
            subcategoriesEl.style.maxHeight = subcategoriesEl.scrollHeight + 'px';
        } else {
            subcategoriesEl.classList.add('collapsed');
            expandIcon.classList.remove('expanded');
            subcategoriesEl.style.maxHeight = '0';
        }
    }

    /**
     * Show alert message
     */
    showAlert(message, type = 'info') {
        const alert = document.createElement('div');
        alert.className = `alert ${type}`;
        alert.textContent = message;

        this.alertContainer.appendChild(alert);

        // Trigger animation
        setTimeout(() => alert.classList.add('show'), 10);

        // Auto remove after 5 seconds
        setTimeout(() => {
            alert.classList.remove('show');
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 300);
        }, 5000);
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.budgetApp = new BudgetApp();
});
