/**
 * Budget Allocator - Mobile-First Responsive Styles
 * WCAG 2.1 AA Compliant Design
 */

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #1a1a1a;
    background-color: #f8f9fa;
    min-height: 100vh;
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for accessibility */
*:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 16px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.app-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.income-display {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.income-amount {
    font-weight: 700;
    font-size: 1.1rem;
}

/* Main Content */
.main {
    padding: 24px 0;
}

/* Sections */
.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: #2c3e50;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    gap: 16px;
}

/* Income Section */
.income-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.input-group {
    margin-bottom: 16px;
}

.input-label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: #495057;
}

.currency-input {
    position: relative;
    display: flex;
    align-items: center;
}

.currency-symbol {
    position: absolute;
    left: 12px;
    font-weight: 600;
    color: #6c757d;
    z-index: 1;
}

.income-input {
    width: 100%;
    padding: 12px 12px 12px 32px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
    background: white;
}

.income-input:focus {
    border-color: #007bff;
}

.income-input:invalid {
    border-color: #dc3545;
}

/* Summary Section */
.summary-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.summary-cards {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 20px;
}

.summary-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.summary-card.remaining {
    background: #e8f5e8;
    border-color: #28a745;
}

.summary-card.over-allocated {
    background: #f8d7da;
    border-color: #dc3545;
}

.card-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 4px;
}

.card-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
}

.remaining .card-value {
    color: #28a745;
}

.over-allocated .card-value {
    color: #dc3545;
}

/* Progress Bar */
.allocation-progress {
    margin-top: 16px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-fill.over-allocated {
    background: linear-gradient(90deg, #dc3545 0%, #c82333 100%);
}

.progress-text {
    text-align: center;
    font-size: 0.875rem;
    color: #6c757d;
}

/* Categories Section */
.categories-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
    min-width: 44px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-icon {
    font-size: 1.1rem;
    font-weight: 700;
}

.add-category-btn {
    white-space: nowrap;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 16px;
}

.empty-state h3 {
    font-size: 1.1rem;
    margin-bottom: 8px;
    color: #495057;
}

.empty-state p {
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    padding: 16px;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 4px;
    color: #6c757d;
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #495057;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid #e9ecef;
    justify-content: flex-end;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: #495057;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
    background: white;
}

.form-input:focus,
.form-textarea:focus {
    border-color: #007bff;
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.char-counter {
    text-align: right;
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 4px;
}

/* Error Messages */
.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 4px;
    min-height: 1.2rem;
}

/* Alert System */
.alert-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
    max-width: 300px;
}

.alert {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #007bff;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.alert.show {
    transform: translateX(0);
}

.alert.success {
    border-left-color: #28a745;
}

.alert.error {
    border-left-color: #dc3545;
}

.alert.warning {
    border-left-color: #ffc107;
}

/* Categories List */
.categories-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.category-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.category-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-header {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    background: white;
}

.category-info {
    flex: 1;
    min-width: 0;
}

.category-name {
    font-weight: 600;
    font-size: 1rem;
    color: #2c3e50;
    margin-bottom: 4px;
    word-break: break-word;
}

.category-amount {
    font-size: 1.1rem;
    font-weight: 700;
    color: #007bff;
}

.category-description {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 4px;
    line-height: 1.4;
}

.category-progress {
    margin-top: 8px;
}

.category-progress-bar {
    width: 100%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.category-progress-fill {
    height: 100%;
    background: #007bff;
    transition: width 0.3s ease;
}

.category-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.btn-small {
    padding: 8px 12px;
    font-size: 0.8rem;
    min-height: 36px;
    min-width: 36px;
}

.btn-icon-only {
    padding: 8px;
    min-height: 36px;
    min-width: 36px;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-edit {
    background: #ffc107;
    color: #212529;
}

.btn-edit:hover {
    background: #e0a800;
}

/* Subcategories */
.subcategories {
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.subcategory-header {
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    font-weight: 500;
    color: #495057;
    background: #e9ecef;
}

.subcategory-list {
    padding: 8px;
}

.subcategory-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

.subcategory-item:last-child {
    margin-bottom: 0;
}

.subcategory-info {
    flex: 1;
    min-width: 0;
}

.subcategory-name {
    font-weight: 500;
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 2px;
}

.subcategory-amount {
    font-size: 0.95rem;
    font-weight: 600;
    color: #007bff;
}

.subcategory-actions {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
}

/* Expandable sections */
.expandable {
    transition: max-height 0.3s ease;
    overflow: hidden;
}

.expandable.collapsed {
    max-height: 0;
}

.expand-icon {
    transition: transform 0.2s ease;
    font-size: 0.8rem;
    color: #6c757d;
}

.expand-icon.expanded {
    transform: rotate(180deg);
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */

/* Small phones (320px) */
@media (min-width: 320px) {
    .summary-cards {
        grid-template-columns: 1fr 1fr;
    }

    .summary-card:last-child {
        grid-column: 1 / -1;
    }

    .category-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .category-actions {
        align-self: flex-end;
    }
}

/* Standard phones (375px) */
@media (min-width: 375px) {
    .container {
        padding: 0 20px;
    }

    .section-header {
        flex-wrap: nowrap;
    }

    .add-category-btn {
        flex-shrink: 0;
    }

    .category-header {
        flex-direction: row;
        align-items: center;
    }
}

/* Tablets and larger (768px) */
@media (min-width: 768px) {
    .container {
        padding: 0 32px;
    }

    .main {
        padding: 32px 0;
    }

    .summary-cards {
        grid-template-columns: repeat(3, 1fr);
    }

    .summary-card:last-child {
        grid-column: auto;
    }

    .modal-footer {
        justify-content: flex-end;
    }

    .btn {
        padding: 12px 24px;
    }

    .categories-list {
        gap: 16px;
    }

    .category-item {
        border-radius: 12px;
    }
}
